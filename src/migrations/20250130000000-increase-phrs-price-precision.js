'use strict';

/**
 * 增加 pricePhrs 字段精度的数据库迁移
 * 
 * 将 pricePhrs 字段从 DECIMAL(20, 4) 修改为 DECIMAL(30, 8)
 * 以支持极高汇率下的8位小数精度计算
 */

module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('🔄 开始增加 pricePhrs 字段精度...');
    
    try {
      // 1. 检查表是否存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('iap_products')) {
        console.log('⚠️  iap_products 表不存在，跳过迁移');
        return;
      }

      // 2. 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('iap_products');
      if (!tableDescription.pricePhrs) {
        console.log('⚠️  pricePhrs 字段不存在，跳过迁移');
        return;
      }

      // 3. 检查当前字段精度
      const currentType = tableDescription.pricePhrs.type;
      console.log(`📊 当前 pricePhrs 字段类型: ${currentType}`);

      // 4. 备份当前数据（记录修改前的状态）
      console.log('💾 备份当前数据状态...');
      const backupQuery = `
        SELECT COUNT(*) as total_products,
               COUNT(pricePhrs) as products_with_phrs_price,
               MIN(pricePhrs) as min_phrs_price,
               MAX(pricePhrs) as max_phrs_price,
               AVG(pricePhrs) as avg_phrs_price
        FROM iap_products
        WHERE priceUsd > 0
      `;
      
      const [backupData] = await queryInterface.sequelize.query(backupQuery, {
        type: Sequelize.QueryTypes.SELECT
      });
      
      console.log('📋 迁移前数据统计:');
      console.log(`  总产品数: ${backupData.total_products}`);
      console.log(`  有PHRS价格的产品: ${backupData.products_with_phrs_price}`);
      console.log(`  PHRS价格范围: ${backupData.min_phrs_price} - ${backupData.max_phrs_price}`);
      console.log(`  平均PHRS价格: ${backupData.avg_phrs_price}`);

      // 5. 修改字段精度
      console.log('🔧 修改 pricePhrs 字段精度为 DECIMAL(30, 8)...');
      await queryInterface.changeColumn('iap_products', 'pricePhrs', {
        type: Sequelize.DECIMAL(30, 8),
        allowNull: true,
        comment: 'PHRS价格，基于USD价格和PHRS汇率计算，支持8位小数精度'
      });

      // 6. 验证修改结果
      console.log('✅ 验证字段修改结果...');
      const updatedDescription = await queryInterface.describeTable('iap_products');
      const newType = updatedDescription.pricePhrs.type;
      console.log(`📊 修改后 pricePhrs 字段类型: ${newType}`);

      // 7. 验证数据完整性
      const [verifyData] = await queryInterface.sequelize.query(backupQuery, {
        type: Sequelize.QueryTypes.SELECT
      });
      
      console.log('📋 迁移后数据统计:');
      console.log(`  总产品数: ${verifyData.total_products}`);
      console.log(`  有PHRS价格的产品: ${verifyData.products_with_phrs_price}`);
      console.log(`  PHRS价格范围: ${verifyData.min_phrs_price} - ${verifyData.max_phrs_price}`);

      // 8. 数据完整性检查
      if (backupData.total_products !== verifyData.total_products) {
        throw new Error('数据完整性检查失败：产品总数不匹配');
      }

      if (backupData.products_with_phrs_price !== verifyData.products_with_phrs_price) {
        console.log('⚠️  有PHRS价格的产品数量发生变化，这可能是正常的');
      }

      console.log('✅ pricePhrs 字段精度增加完成');
      console.log('💡 现在支持8位小数精度，可以处理极高汇率的计算');

    } catch (error) {
      console.error('❌ 迁移失败:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    console.log('🔄 开始回滚 pricePhrs 字段精度...');
    
    try {
      // 1. 检查表是否存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('iap_products')) {
        console.log('⚠️  iap_products 表不存在，跳过回滚');
        return;
      }

      // 2. 检查字段是否存在
      const tableDescription = await queryInterface.describeTable('iap_products');
      if (!tableDescription.pricePhrs) {
        console.log('⚠️  pricePhrs 字段不存在，跳过回滚');
        return;
      }

      // 3. 警告：回滚可能导致精度丢失
      console.log('⚠️  警告：回滚到4位精度可能导致数据精度丢失');
      
      // 4. 检查是否有会丢失精度的数据
      const precisionCheckQuery = `
        SELECT COUNT(*) as count_with_high_precision
        FROM iap_products 
        WHERE pricePhrs IS NOT NULL 
        AND pricePhrs != ROUND(pricePhrs, 4)
      `;
      
      const [precisionCheck] = await queryInterface.sequelize.query(precisionCheckQuery, {
        type: Sequelize.QueryTypes.SELECT
      });

      if (precisionCheck.count_with_high_precision > 0) {
        console.log(`⚠️  发现 ${precisionCheck.count_with_high_precision} 个产品的PHRS价格精度超过4位小数`);
        console.log('⚠️  回滚将导致这些数据的精度丢失');
        
        // 显示会受影响的数据
        const affectedDataQuery = `
          SELECT name, priceUsd, pricePhrs, ROUND(pricePhrs, 4) as rounded_phrs
          FROM iap_products 
          WHERE pricePhrs IS NOT NULL 
          AND pricePhrs != ROUND(pricePhrs, 4)
          LIMIT 5
        `;
        
        const affectedData = await queryInterface.sequelize.query(affectedDataQuery, {
          type: Sequelize.QueryTypes.SELECT
        });
        
        console.log('📋 受影响的数据示例:');
        affectedData.forEach((product, index) => {
          console.log(`  ${index + 1}. ${product.name}:`);
          console.log(`     当前PHRS价格: ${product.pricePhrs}`);
          console.log(`     回滚后价格: ${product.rounded_phrs}`);
        });
      }

      // 5. 备份当前数据状态
      const backupQuery = `
        SELECT COUNT(*) as total_products,
               COUNT(pricePhrs) as products_with_phrs_price
        FROM iap_products
      `;
      
      const [backupData] = await queryInterface.sequelize.query(backupQuery, {
        type: Sequelize.QueryTypes.SELECT
      });

      // 6. 执行回滚：将字段改回 DECIMAL(20, 4)
      console.log('🔧 回滚 pricePhrs 字段精度为 DECIMAL(20, 4)...');
      await queryInterface.changeColumn('iap_products', 'pricePhrs', {
        type: Sequelize.DECIMAL(20, 4),
        allowNull: true,
        comment: 'PHRS价格，基于USD价格和PHRS汇率计算'
      });

      // 7. 验证回滚结果
      const [verifyData] = await queryInterface.sequelize.query(backupQuery, {
        type: Sequelize.QueryTypes.SELECT
      });

      if (backupData.total_products !== verifyData.total_products) {
        throw new Error('回滚数据完整性检查失败：产品总数不匹配');
      }

      console.log('✅ pricePhrs 字段精度回滚完成');
      console.log('⚠️  注意：如果之前有8位精度的数据，现在已被四舍五入到4位精度');

    } catch (error) {
      console.error('❌ 回滚失败:', error);
      throw error;
    }
  }
};
