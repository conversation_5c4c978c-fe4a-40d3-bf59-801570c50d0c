import { Job } from "bullmq";
import { processAutoCollectChests } from "../services/jackpotChestService";
import { DynamicControlledWorker } from "./DynamicControlledWorker";

const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[JackpotChestWorker] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[JackpotChestWorker] ${message}`, ...args);
  },
};

console.log('jackpotChestWorker.ts loaded');

// 处理自动领取宝箱
async function handleAutoCollect() {
  try {
    await processAutoCollectChests();
  } catch (error) {
    logger.error('处理自动领取宝箱失败:', error);
    throw error;
  }
}

// 处理Jackpot初始化
async function handleInitializeJackpot() {
  try {
    // await initializeJackpotPools();
    logger.info('Jackpot初始化完成');
  } catch (error) {
    logger.error('Jackpot初始化失败:', error);
    throw error;
  }
}

// 任务处理器
async function processJackpotTask(job: Job) {
  logger.info(`处理任务: ${job.name}`);

  switch (job.name) {
    case 'initialize-jackpot':
      await handleInitializeJackpot();
      break;
    case 'auto-collect-chests':
      await handleAutoCollect();
      break;
    default:
      logger.info(`未知任务类型: ${job.name}`);
      throw new Error(`未知任务类型: ${job.name}`);
  }

  return { success: true, processedAt: new Date().toISOString() };
}

// 创建动态控制的Worker
const jackpotWorker = new DynamicControlledWorker({
  queueName: 'jackpot-chest-queue',
  taskType: 'jackpot',
  workerName: 'JackpotChestWorker',
  processor: processJackpotTask,
  options: {
    concurrency: 1
    // connection 已经在 DynamicControlledWorker 中设置，不需要重复
  },
  checkInterval: 30000 // 30秒检查一次环境变量
});

// 导出worker实例
export default jackpotWorker;

// 定义消息类型接口
interface WorkerMessage {
  type: string;
  [key: string]: any;
}

// 监听主进程发送的消息
process.on('message', async (message: WorkerMessage) => {
  if (message && message.type === 'shutdown') {
    console.log('JackpotChestWorker 收到关闭信号，正在清理资源...');

    try {
      await jackpotWorker.close();
      console.log('JackpotChestWorker 资源清理完毕，准备退出');

      if (process.send) {
        process.send({ type: 'ready_to_exit' });
      }

      process.exit(0);
    } catch (err) {
      console.error('JackpotChestWorker 清理资源失败:', err);
      process.exit(1);
    }
  }
});

// 监听 SIGTERM 信号
process.on('SIGTERM', async () => {
  console.log('JackpotChestWorker 收到 SIGTERM 信号，准备退出');
  try {
    await jackpotWorker.close();
  } catch (err) {
    console.error('关闭 worker 失败:', err);
  }
  process.exit(0);
});

// 监听 SIGINT 信号
process.on('SIGINT', async () => {
  console.log('JackpotChestWorker 收到 SIGINT 信号，准备退出');
  try {
    await jackpotWorker.close();
  } catch (err) {
    console.error('关闭 worker 失败:', err);
  }
  process.exit(0);
});

// 添加导出的初始化函数
export async function initializeWorker(_queue: any) {
  console.log('初始化 Jackpot 宝箱处理器...');
  // DynamicControlledWorker 已经在模块加载时创建并自动开始控制
  return jackpotWorker;
}

// 导出worker实例用于外部控制
export { jackpotWorker };