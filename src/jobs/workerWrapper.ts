/**
 * Worker包装器工具
 * 
 * 提供统一的环境变量检查和任务执行包装功能
 */

import { Job } from "bullmq";
import { backgroundTaskController } from "../services/BackgroundTaskController";

export interface WorkerExecutionResult {
  success: boolean;
  skipped?: boolean;
  reason?: string;
  data?: any;
}

/**
 * 包装worker任务执行函数，添加环境变量检查
 */
export function wrapWorkerTask(
  taskType: string,
  workerName: string,
  taskHandler: (job: Job) => Promise<any>
): (job: Job) => Promise<WorkerExecutionResult> {
  return async (job: Job): Promise<WorkerExecutionResult> => {
    // 检查是否应该执行该类型的任务
    if (!backgroundTaskController.shouldRunTask(taskType)) {
      console.log(`⏸️  [${workerName}] 任务 ${job.name} 已被环境变量禁用，跳过执行`);
      return {
        success: true,
        skipped: true,
        reason: `${taskType} tasks disabled by environment variable`
      };
    }

    try {
      console.log(`🔄 [${workerName}] 开始处理任务: ${job.name}`);
      const result = await taskHandler(job);
      console.log(`✅ [${workerName}] 任务 ${job.name} 处理完成`);
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error(`❌ [${workerName}] 任务 ${job.name} 处理失败:`, error);
      throw error; // 重新抛出错误，让BullMQ处理重试逻辑
    }
  };
}

/**
 * 创建带有环境变量检查的worker处理函数
 */
export function createWorkerHandler(
  taskType: string,
  workerName: string,
  taskHandlers: { [taskName: string]: (job: Job) => Promise<any> }
): (job: Job) => Promise<WorkerExecutionResult> {
  return wrapWorkerTask(taskType, workerName, async (job: Job) => {
    const handler = taskHandlers[job.name];
    
    if (!handler) {
      throw new Error(`未知任务类型: ${job.name}`);
    }
    
    return await handler(job);
  });
}

/**
 * 检查特定任务是否应该执行
 */
export function shouldExecuteTask(taskType: string): boolean {
  return backgroundTaskController.shouldRunTask(taskType);
}

/**
 * 获取任务跳过的日志消息
 */
export function getSkipMessage(workerName: string, taskName: string, taskType: string): string {
  return `⏸️  [${workerName}] 任务 ${taskName} (${taskType}) 已被环境变量禁用，跳过执行`;
}

/**
 * 记录任务开始执行的日志
 */
export function logTaskStart(workerName: string, taskName: string): void {
  console.log(`🔄 [${workerName}] 开始处理任务: ${taskName}`);
}

/**
 * 记录任务完成的日志
 */
export function logTaskComplete(workerName: string, taskName: string): void {
  console.log(`✅ [${workerName}] 任务 ${taskName} 处理完成`);
}

/**
 * 记录任务失败的日志
 */
export function logTaskError(workerName: string, taskName: string, error: any): void {
  console.error(`❌ [${workerName}] 任务 ${taskName} 处理失败:`, error);
}

/**
 * 为定时任务创建包装器
 */
export function wrapScheduledTask<T extends (...args: any[]) => any>(
  taskType: string,
  taskName: string,
  taskFunction: T
): T {
  return ((...args: any[]) => {
    if (!backgroundTaskController.shouldRunTask(taskType)) {
      console.log(`⏸️  定时任务 "${taskName}" (${taskType}) 已被环境变量禁用，跳过执行`);
      return Promise.resolve();
    }
    
    console.log(`🔄 开始执行定时任务: ${taskName}`);
    return taskFunction(...args);
  }) as T;
}

/**
 * 为cron任务创建包装器
 */
export function wrapCronTask(
  taskType: string,
  taskName: string,
  taskFunction: () => Promise<void>
): () => Promise<void> {
  return async () => {
    if (!backgroundTaskController.shouldRunTask(taskType)) {
      console.log(`⏸️  Cron任务 "${taskName}" (${taskType}) 已被环境变量禁用，跳过执行`);
      return;
    }
    
    try {
      console.log(`🔄 开始执行Cron任务: ${taskName}`);
      await taskFunction();
      console.log(`✅ Cron任务 ${taskName} 执行完成`);
    } catch (error) {
      console.error(`❌ Cron任务 ${taskName} 执行失败:`, error);
      throw error;
    }
  };
}
