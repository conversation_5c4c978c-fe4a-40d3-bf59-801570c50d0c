/**
 * 后台任务控制服务
 * 
 * 该服务负责管理所有后台任务的启用/禁用状态，
 * 提供统一的环境变量检查接口，避免在多个环境中重复执行相同的任务。
 */

import { getEnvBoolean, getEnvVar } from '../config/env';

export interface TaskControlConfig {
  // 总开关
  enableBackgroundTasks: boolean;
  
  // 主要功能开关
  enableCronJobs: boolean;
  enableQueueWorkers: boolean;
  enableScheduledJobs: boolean;
  enablePaymentMonitoring: boolean;
  enableAccountSubscription: boolean;
  
  // 细粒度控制
  enableLotteryJobs: boolean;
  enableRewardJobs: boolean;
  enablePriceUpdateJobs: boolean;
  enableJackpotJobs: boolean;
  enableWithdrawalJobs: boolean;
  enableRebateJobs: boolean;
}

export class BackgroundTaskController {
  private static instance: BackgroundTaskController;
  private config!: TaskControlConfig;
  private environment: string;

  private constructor() {
    this.environment = getEnvVar('NODE_ENV', 'development') || 'development';
    this.loadConfig();
  }

  public static getInstance(): BackgroundTaskController {
    if (!BackgroundTaskController.instance) {
      BackgroundTaskController.instance = new BackgroundTaskController();
    }
    return BackgroundTaskController.instance;
  }

  /**
   * 从环境变量加载配置
   */
  private loadConfig(): void {
    // 总开关 - 默认在生产环境启用，开发环境禁用
    const defaultBackgroundTasks = this.environment === 'production';
    
    this.config = {
      // 总开关
      enableBackgroundTasks: getEnvBoolean('ENABLE_BACKGROUND_TASKS', defaultBackgroundTasks),
      
      // 主要功能开关 - 如果总开关关闭，这些都会被忽略
      enableCronJobs: getEnvBoolean('ENABLE_CRON_JOBS', true),
      enableQueueWorkers: getEnvBoolean('ENABLE_QUEUE_WORKERS', true),
      enableScheduledJobs: getEnvBoolean('ENABLE_SCHEDULED_JOBS', true),
      enablePaymentMonitoring: getEnvBoolean('ENABLE_PAYMENT_MONITORING', true),
      enableAccountSubscription: getEnvBoolean('ENABLE_ACCOUNT_SUBSCRIPTION', true),
      
      // 细粒度控制
      enableLotteryJobs: getEnvBoolean('ENABLE_LOTTERY_JOBS', true),
      enableRewardJobs: getEnvBoolean('ENABLE_REWARD_JOBS', true),
      enablePriceUpdateJobs: getEnvBoolean('ENABLE_PRICE_UPDATE_JOBS', true),
      enableJackpotJobs: getEnvBoolean('ENABLE_JACKPOT_JOBS', true),
      enableWithdrawalJobs: getEnvBoolean('ENABLE_WITHDRAWAL_JOBS', true),
      enableRebateJobs: getEnvBoolean('ENABLE_REBATE_JOBS', true),
    };

    this.logConfiguration();
  }

  /**
   * 记录当前配置状态
   */
  private logConfiguration(): void {
    const envInfo = getEnvVar('ENV_FILE', '.env');
    console.log(`🔧 [BackgroundTaskController] 环境: ${this.environment}, 配置文件: ${envInfo}`);
    console.log(`🔧 [BackgroundTaskController] 后台任务控制配置:`);
    console.log(`   - 总开关: ${this.config.enableBackgroundTasks ? '✅ 启用' : '❌ 禁用'}`);
    
    if (this.config.enableBackgroundTasks) {
      console.log(`   - 定时任务: ${this.config.enableCronJobs ? '✅' : '❌'}`);
      console.log(`   - 队列处理: ${this.config.enableQueueWorkers ? '✅' : '❌'}`);
      console.log(`   - 调度任务: ${this.config.enableScheduledJobs ? '✅' : '❌'}`);
      console.log(`   - 支付监控: ${this.config.enablePaymentMonitoring ? '✅' : '❌'}`);
      console.log(`   - 账户订阅: ${this.config.enableAccountSubscription ? '✅' : '❌'}`);
      console.log(`   - 抽奖任务: ${this.config.enableLotteryJobs ? '✅' : '❌'}`);
      console.log(`   - 奖励任务: ${this.config.enableRewardJobs ? '✅' : '❌'}`);
      console.log(`   - 价格更新: ${this.config.enablePriceUpdateJobs ? '✅' : '❌'}`);
      console.log(`   - Jackpot: ${this.config.enableJackpotJobs ? '✅' : '❌'}`);
      console.log(`   - 提现任务: ${this.config.enableWithdrawalJobs ? '✅' : '❌'}`);
      console.log(`   - 返利任务: ${this.config.enableRebateJobs ? '✅' : '❌'}`);
    } else {
      console.log(`   ⚠️  所有后台任务已禁用`);
    }
  }

  /**
   * 检查是否应该执行后台任务（总开关）
   */
  public shouldRunBackgroundTasks(): boolean {
    return this.config.enableBackgroundTasks;
  }

  /**
   * 检查是否应该执行定时任务
   */
  public shouldRunCronJobs(): boolean {
    return this.config.enableBackgroundTasks && this.config.enableCronJobs;
  }

  /**
   * 检查是否应该执行队列处理
   */
  public shouldRunQueueWorkers(): boolean {
    return this.config.enableBackgroundTasks && this.config.enableQueueWorkers;
  }

  /**
   * 检查是否应该执行调度任务
   */
  public shouldRunScheduledJobs(): boolean {
    return this.config.enableBackgroundTasks && this.config.enableScheduledJobs;
  }

  /**
   * 检查是否应该执行支付监控
   */
  public shouldRunPaymentMonitoring(): boolean {
    return this.config.enableBackgroundTasks && this.config.enablePaymentMonitoring;
  }

  /**
   * 检查是否应该执行账户订阅服务
   */
  public shouldRunAccountSubscription(): boolean {
    return this.config.enableBackgroundTasks && this.config.enableAccountSubscription;
  }

  /**
   * 检查是否应该执行抽奖相关任务
   */
  public shouldRunLotteryJobs(): boolean {
    return this.config.enableBackgroundTasks && 
           this.config.enableScheduledJobs && 
           this.config.enableLotteryJobs;
  }

  /**
   * 检查是否应该执行奖励分发任务
   */
  public shouldRunRewardJobs(): boolean {
    return this.config.enableBackgroundTasks && 
           this.config.enableCronJobs && 
           this.config.enableRewardJobs;
  }

  /**
   * 检查是否应该执行价格更新任务
   */
  public shouldRunPriceUpdateJobs(): boolean {
    return this.config.enableBackgroundTasks && 
           this.config.enableCronJobs && 
           this.config.enablePriceUpdateJobs;
  }

  /**
   * 检查是否应该执行Jackpot相关任务
   */
  public shouldRunJackpotJobs(): boolean {
    return this.config.enableBackgroundTasks && 
           this.config.enableQueueWorkers && 
           this.config.enableJackpotJobs;
  }

  /**
   * 检查是否应该执行提现任务
   */
  public shouldRunWithdrawalJobs(): boolean {
    return this.config.enableBackgroundTasks && 
           this.config.enableQueueWorkers && 
           this.config.enableWithdrawalJobs;
  }

  /**
   * 检查是否应该执行返利任务
   */
  public shouldRunRebateJobs(): boolean {
    return this.config.enableBackgroundTasks && 
           this.config.enableCronJobs && 
           this.config.enableRebateJobs;
  }

  /**
   * 获取当前配置
   */
  public getConfig(): TaskControlConfig {
    return { ...this.config };
  }

  /**
   * 重新加载配置（用于运行时更新）
   */
  public reloadConfig(): void {
    this.loadConfig();
  }

  /**
   * 获取环境信息
   */
  public getEnvironmentInfo(): { environment: string; configFile: string } {
    return {
      environment: this.environment,
      configFile: getEnvVar('ENV_FILE', '.env') || '.env'
    };
  }

  /**
   * 检查特定任务类型是否应该执行
   */
  public shouldRunTask(taskType: string): boolean {
    switch (taskType.toLowerCase()) {
      case 'lottery':
      case 'lottery-result':
        return this.shouldRunLotteryJobs();
      
      case 'moof-holders-reward':
      case 'personal-kol-reward':
      case 'team-kol-reward':
        return this.shouldRunRewardJobs();
      
      case 'kaia-price-update':
      case 'phrs-price-update':
        return this.shouldRunPriceUpdateJobs();
      
      case 'jackpot':
      case 'jackpot-chest':
        return this.shouldRunJackpotJobs();
      
      case 'withdrawal':
      case 'ton-withdrawal':
        return this.shouldRunWithdrawalJobs();
      
      case 'daily-rebate-settlement':
        return this.shouldRunRebateJobs();
      
      case 'payment-status':
        return this.shouldRunPaymentMonitoring();
      
      case 'account-subscription':
        return this.shouldRunAccountSubscription();
      
      default:
        // 对于未知任务类型，使用总开关
        return this.shouldRunBackgroundTasks();
    }
  }

  /**
   * 创建任务执行包装器
   */
  public wrapTask<T extends (...args: any[]) => any>(
    taskType: string,
    taskFunction: T,
    taskName?: string
  ): T {
    return ((...args: any[]) => {
      if (!this.shouldRunTask(taskType)) {
        const name = taskName || taskType;
        console.log(`⏸️  [BackgroundTaskController] 任务 "${name}" 已被环境变量禁用，跳过执行`);
        return Promise.resolve();
      }
      return taskFunction(...args);
    }) as T;
  }
}

// 导出单例实例
export const backgroundTaskController = BackgroundTaskController.getInstance();
