/**
 * Worker管理器
 * 
 * 统一管理所有DynamicControlledWorker实例，
 * 提供批量控制、状态监控和配置重载功能。
 */

import { DynamicControlledWorker, WorkerStatus } from '../jobs/DynamicControlledWorker';
import { backgroundTaskController } from './BackgroundTaskController';

export interface WorkerManagerStatus {
  totalWorkers: number;
  activeWorkers: number;
  pausedWorkers: number;
  workers: { [name: string]: WorkerStatus };
  lastUpdate: Date;
}

export class WorkerManager {
  private static instance: WorkerManager;
  private workers: Map<string, DynamicControlledWorker> = new Map();
  private isShuttingDown: boolean = false;

  private constructor() {
    this.setupGracefulShutdown();
  }

  public static getInstance(): WorkerManager {
    if (!WorkerManager.instance) {
      WorkerManager.instance = new WorkerManager();
    }
    return WorkerManager.instance;
  }

  /**
   * 注册Worker
   */
  registerWorker(name: string, worker: DynamicControlledWorker): void {
    if (this.workers.has(name)) {
      console.warn(`⚠️  Worker "${name}" 已存在，将被替换`);
    }
    
    this.workers.set(name, worker);
    console.log(`✅ Worker "${name}" 已注册到管理器`);
  }

  /**
   * 注销Worker
   */
  async unregisterWorker(name: string): Promise<void> {
    const worker = this.workers.get(name);
    if (worker) {
      try {
        await worker.close();
        this.workers.delete(name);
        console.log(`🔴 Worker "${name}" 已注销`);
      } catch (error) {
        console.error(`❌ 注销Worker "${name}" 失败:`, error);
        throw error;
      }
    }
  }

  /**
   * 获取Worker
   */
  getWorker(name: string): DynamicControlledWorker | undefined {
    return this.workers.get(name);
  }

  /**
   * 获取所有Worker名称
   */
  getWorkerNames(): string[] {
    return Array.from(this.workers.keys());
  }

  /**
   * 暂停所有Worker
   */
  async pauseAllWorkers(global = false): Promise<void> {
    console.log(`⏸️  开始${global ? '全局' : '本地'}暂停所有Worker...`);
    
    const promises = Array.from(this.workers.entries()).map(async ([name, worker]) => {
      try {
        await worker.pause(global);
        console.log(`⏸️  Worker "${name}" 已暂停`);
      } catch (error) {
        console.error(`❌ 暂停Worker "${name}" 失败:`, error);
      }
    });

    await Promise.allSettled(promises);
    console.log('✅ 所有Worker暂停操作完成');
  }

  /**
   * 恢复所有Worker
   */
  async resumeAllWorkers(): Promise<void> {
    console.log('▶️  开始恢复所有Worker...');
    
    const promises = Array.from(this.workers.entries()).map(async ([name, worker]) => {
      try {
        await worker.resume();
        console.log(`▶️  Worker "${name}" 已恢复`);
      } catch (error) {
        console.error(`❌ 恢复Worker "${name}" 失败:`, error);
      }
    });

    await Promise.allSettled(promises);
    console.log('✅ 所有Worker恢复操作完成');
  }

  /**
   * 重新加载所有Worker配置
   */
  async reloadAllConfigs(): Promise<void> {
    console.log('🔄 开始重新加载所有Worker配置...');
    
    // 首先重新加载BackgroundTaskController配置
    backgroundTaskController.reloadConfig();
    
    const promises = Array.from(this.workers.entries()).map(async ([name, worker]) => {
      try {
        await worker.reloadConfig();
        console.log(`🔄 Worker "${name}" 配置已重新加载`);
      } catch (error) {
        console.error(`❌ 重新加载Worker "${name}" 配置失败:`, error);
      }
    });

    await Promise.allSettled(promises);
    console.log('✅ 所有Worker配置重新加载完成');
  }

  /**
   * 获取所有Worker状态
   */
  async getAllWorkersStatus(): Promise<WorkerManagerStatus> {
    const workersStatus: { [name: string]: WorkerStatus } = {};
    const statusResults: WorkerStatus[] = [];

    const promises = Array.from(this.workers.entries()).map(async ([name, worker]) => {
      try {
        const status = await worker.getStatus();
        workersStatus[name] = status;
        statusResults.push(status);
      } catch (error) {
        console.error(`❌ 获取Worker "${name}" 状态失败:`, error);
        const errorStatus: WorkerStatus = {
          isPaused: false,
          isRunning: false,
          shouldRun: false,
          lastCheck: new Date(),
          taskType: 'unknown',
          workerName: name
        };
        workersStatus[name] = errorStatus;
        statusResults.push(errorStatus);
      }
    });

    await Promise.allSettled(promises);

    // 统计状态（避免竞态条件）
    const activeCount = statusResults.filter(s => s.isRunning && !s.isPaused).length;
    const pausedCount = statusResults.filter(s => s.isPaused).length;

    return {
      totalWorkers: this.workers.size,
      activeWorkers: activeCount,
      pausedWorkers: pausedCount,
      workers: workersStatus,
      lastUpdate: new Date()
    };
  }

  /**
   * 按任务类型暂停Worker
   */
  async pauseWorkersByTaskType(taskType: string, global = false): Promise<void> {
    console.log(`⏸️  开始暂停任务类型为 "${taskType}" 的Worker...`);
    
    const promises = Array.from(this.workers.values())
      .filter(worker => worker.getTaskType() === taskType)
      .map(async (worker) => {
        try {
          await worker.pause(global);
          console.log(`⏸️  Worker "${worker.getWorkerName()}" (${taskType}) 已暂停`);
        } catch (error) {
          console.error(`❌ 暂停Worker "${worker.getWorkerName()}" 失败:`, error);
        }
      });

    await Promise.allSettled(promises);
    console.log(`✅ 任务类型 "${taskType}" 的Worker暂停操作完成`);
  }

  /**
   * 按任务类型恢复Worker
   */
  async resumeWorkersByTaskType(taskType: string): Promise<void> {
    console.log(`▶️  开始恢复任务类型为 "${taskType}" 的Worker...`);
    
    const promises = Array.from(this.workers.values())
      .filter(worker => worker.getTaskType() === taskType)
      .map(async (worker) => {
        try {
          await worker.resume();
          console.log(`▶️  Worker "${worker.getWorkerName()}" (${taskType}) 已恢复`);
        } catch (error) {
          console.error(`❌ 恢复Worker "${worker.getWorkerName()}" 失败:`, error);
        }
      });

    await Promise.allSettled(promises);
    console.log(`✅ 任务类型 "${taskType}" 的Worker恢复操作完成`);
  }

  /**
   * 关闭所有Worker
   */
  async closeAllWorkers(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }
    
    this.isShuttingDown = true;
    console.log('🔴 开始关闭所有Worker...');
    
    const promises = Array.from(this.workers.entries()).map(async ([name, worker]) => {
      try {
        await worker.close();
        console.log(`🔴 Worker "${name}" 已关闭`);
      } catch (error) {
        console.error(`❌ 关闭Worker "${name}" 失败:`, error);
      }
    });

    await Promise.allSettled(promises);
    this.workers.clear();
    console.log('✅ 所有Worker已关闭');
  }

  /**
   * 打印状态报告
   */
  async printStatusReport(): Promise<void> {
    const status = await this.getAllWorkersStatus();
    
    console.log('\n📊 Worker管理器状态报告');
    console.log('=' .repeat(50));
    console.log(`总Worker数量: ${status.totalWorkers}`);
    console.log(`活跃Worker: ${status.activeWorkers}`);
    console.log(`暂停Worker: ${status.pausedWorkers}`);
    console.log(`更新时间: ${status.lastUpdate.toISOString()}`);
    
    console.log('\n📋 详细状态:');
    for (const [name, workerStatus] of Object.entries(status.workers)) {
      const statusIcon = workerStatus.isPaused ? '⏸️' : (workerStatus.isRunning ? '▶️' : '⏹️');
      const shouldRunIcon = workerStatus.shouldRun ? '✅' : '❌';
      console.log(`  ${statusIcon} ${name} (${workerStatus.taskType}) - 应该运行: ${shouldRunIcon}`);
    }
    console.log('');
  }

  /**
   * 设置优雅关闭
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      console.log(`\n收到 ${signal} 信号，开始优雅关闭Worker管理器...`);
      await this.closeAllWorkers();
      process.exit(0);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    
    // 处理未捕获的异常
    process.on('uncaughtException', async (error) => {
      console.error('未捕获的异常:', error);
      await this.closeAllWorkers();
      process.exit(1);
    });

    process.on('unhandledRejection', async (reason) => {
      console.error('未处理的Promise拒绝:', reason);
      await this.closeAllWorkers();
      process.exit(1);
    });
  }
}

// 导出单例实例
export const workerManager = WorkerManager.getInstance();
